"""
API端点测试

测试FastAPI端点的功能和响应
"""

import os
import sys
import unittest
import tempfile
import base64
from pathlib import Path
import numpy as np
import cv2
from fastapi.testclient import TestClient

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(Path(__file__).parent.parent))

# 模拟导入（避免实际启动服务）
try:
    from app import create_app
    HAS_APP = True
except ImportError:
    HAS_APP = False
    print("警告: 无法导入app模块，跳过API测试")


class TestAPIEndpoints(unittest.TestCase):
    """API端点测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        if not HAS_APP:
            cls.skipTest(cls, "app模块不可用")
            return
        
        # 创建测试客户端
        try:
            app = create_app()
            cls.client = TestClient(app)
        except Exception as e:
            cls.skipTest(cls, f"无法创建测试客户端: {e}")
            return
        
        # 创建测试图像
        cls.test_image = cls.create_test_image()
        cls.test_image_path = cls.save_test_image(cls.test_image)
        cls.test_image_base64 = cls.image_to_base64(cls.test_image)
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        if hasattr(cls, 'test_image_path') and os.path.exists(cls.test_image_path):
            os.unlink(cls.test_image_path)
    
    @staticmethod
    def create_test_image(width=320, height=240):
        """创建测试图像"""
        image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        cv2.rectangle(image, (50, 50), (150, 150), (255, 0, 0), 2)
        cv2.circle(image, (200, 120), 30, (0, 255, 0), 2)
        return image
    
    @staticmethod
    def save_test_image(image):
        """保存测试图像"""
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            cv2.imwrite(f.name, image)
            return f.name
    
    @staticmethod
    def image_to_base64(image):
        """将图像转换为Base64"""
        _, buffer = cv2.imencode('.jpg', image)
        image_bytes = buffer.tobytes()
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def test_root_endpoint(self):
        """测试根端点"""
        response = self.client.get("/")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("message", data)
        self.assertIn("version", data)
        self.assertIn("docs", data)
    
    def test_health_endpoint(self):
        """测试健康检查端点"""
        response = self.client.get("/api/v1/health")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("status", data)
        self.assertIn("model_loaded", data)
        self.assertIn("service_version", data)
        self.assertIn("loaded_models", data)
        self.assertIn("enabled_services", data)
    
    def test_models_endpoint(self):
        """测试模型信息端点"""
        response = self.client.get("/api/v1/models")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIsInstance(data, list)
        
        # 检查模型信息结构
        if len(data) > 0:
            model = data[0]
            self.assertIn("name", model)
            self.assertIn("path", model)
            self.assertIn("type", model)
            self.assertIn("loaded", model)
            self.assertIn("enabled", model)
    
    def test_services_endpoint(self):
        """测试服务状态端点"""
        response = self.client.get("/api/v1/services")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIsInstance(data, list)
        
        # 检查服务信息结构
        if len(data) > 0:
            service = data[0]
            self.assertIn("service_name", service)
            self.assertIn("status", service)
            self.assertIn("healthy", service)
            self.assertIn("message", service)
    
    def test_config_endpoint(self):
        """测试配置信息端点"""
        response = self.client.get("/api/v1/config")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("models", data)
        self.assertIn("enabled_services", data)
        self.assertIn("max_workers", data)
        self.assertIn("max_file_size_mb", data)
        self.assertIn("allowed_extensions", data)
    
    def test_upload_detection_invalid_file(self):
        """测试上传无效文件"""
        # 创建无效文件
        invalid_file = ("test.txt", b"not an image", "text/plain")
        
        response = self.client.post(
            "/api/v1/detect/upload",
            files={"file": invalid_file},
            data={"model_type": "dipper"}
        )
        
        # 应该返回400错误
        self.assertEqual(response.status_code, 400)
    
    def test_base64_detection_invalid_data(self):
        """测试Base64检测无效数据"""
        payload = {
            "model_type": "filter",
            "image_data": "invalid_base64_data",
            "save_result": False
        }
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        
        # 应该返回400错误
        self.assertEqual(response.status_code, 400)
    
    def test_base64_detection_valid_request(self):
        """测试Base64检测有效请求"""
        payload = {
            "model_type": "dipper",
            "image_data": self.test_image_base64,
            "save_result": False,
            "conf": 0.5,
            "iou": 0.4
        }
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        
        # 检查响应状态（可能成功也可能失败，取决于模型是否可用）
        self.assertIn(response.status_code, [200, 400, 500])
        
        data = response.json()
        self.assertIn("success", data)
        self.assertIn("model_name", data)
        self.assertIn("devices", data)
    
    def test_invalid_model_type(self):
        """测试无效模型类型"""
        payload = {
            "model_type": "invalid_model",
            "image_data": self.test_image_base64,
            "save_result": False
        }
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        
        # 应该返回422错误（验证错误）
        self.assertEqual(response.status_code, 422)
    
    def test_cleanup_endpoint(self):
        """测试清理端点"""
        payload = {"days": 1}
        
        response = self.client.post("/api/v1/cleanup", json=payload)
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("success", data)
        self.assertIn("message", data)
        self.assertIn("cleaned_files", data)
        self.assertIn("cutoff_days", data)
    
    def test_download_nonexistent_file(self):
        """测试下载不存在的文件"""
        response = self.client.get("/api/v1/download/nonexistent_file.jpg")
        self.assertEqual(response.status_code, 404)


class TestAPIValidation(unittest.TestCase):
    """API验证测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        if not HAS_APP:
            cls.skipTest(cls, "app模块不可用")
            return
        
        try:
            app = create_app()
            cls.client = TestClient(app)
        except Exception as e:
            cls.skipTest(cls, f"无法创建测试客户端: {e}")
    
    def test_parameter_validation(self):
        """测试参数验证"""
        # 测试置信度超出范围
        payload = {
            "model_type": "dipper",
            "image_data": "dGVzdA==",  # base64 for "test"
            "conf": 1.5,  # 超出范围
            "save_result": False
        }
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        self.assertEqual(response.status_code, 422)
        
        # 测试IoU超出范围
        payload["conf"] = 0.5
        payload["iou"] = -0.1  # 超出范围
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        self.assertEqual(response.status_code, 422)
        
        # 测试图像尺寸超出范围
        payload["iou"] = 0.4
        payload["imgsz"] = 100  # 太小
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        self.assertEqual(response.status_code, 422)
    
    def test_required_fields(self):
        """测试必需字段"""
        # 缺少model_type
        payload = {
            "image_data": "dGVzdA==",
            "save_result": False
        }
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        self.assertEqual(response.status_code, 422)
        
        # 缺少image_data
        payload = {
            "model_type": "dipper",
            "save_result": False
        }
        
        response = self.client.post("/api/v1/detect/base64", json=payload)
        self.assertEqual(response.status_code, 422)


def run_api_tests():
    """运行API测试"""
    print("🧪 API端点测试")
    print("-" * 40)
    
    if not HAS_APP:
        print("⚠️  跳过API测试（app模块不可用）")
        return True
    
    # 运行端点测试
    print("📋 端点功能测试")
    endpoint_suite = unittest.TestLoader().loadTestsFromTestCase(TestAPIEndpoints)
    endpoint_runner = unittest.TextTestRunner(verbosity=1)
    endpoint_result = endpoint_runner.run(endpoint_suite)
    
    print("\n📋 参数验证测试")
    validation_suite = unittest.TestLoader().loadTestsFromTestCase(TestAPIValidation)
    validation_runner = unittest.TextTestRunner(verbosity=1)
    validation_result = validation_runner.run(validation_suite)
    
    return endpoint_result.wasSuccessful() and validation_result.wasSuccessful()


if __name__ == "__main__":
    run_api_tests()
