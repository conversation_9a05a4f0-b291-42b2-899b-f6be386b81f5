"""
综合测试运行器

运行所有YOLO服务测试用例
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(Path(__file__).parent.parent))

# 导入测试模块
try:
    from test_basic_functionality import run_basic_tests
    from test_model_manager import run_model_manager_tests
    from test_concurrent_utils import run_concurrent_utils_tests
    from test_api_endpoints import run_api_tests
    from test_service_integration import YoloServiceTester
    HAS_ALL_TESTS = True
except ImportError as e:
    print(f"警告: 无法导入所有测试模块: {e}")
    HAS_ALL_TESTS = False


def run_unit_tests():
    """运行单元测试"""
    print("🔬 单元测试")
    print("=" * 60)
    
    test_results = []
    
    # 基本功能测试
    try:
        print("\n📋 基本功能测试")
        result = run_basic_tests()
        test_results.append(("基本功能", result))
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        test_results.append(("基本功能", False))
    
    # 模型管理器测试
    try:
        print("\n📋 模型管理器测试")
        result = run_model_manager_tests()
        test_results.append(("模型管理器", result))
    except Exception as e:
        print(f"❌ 模型管理器测试失败: {e}")
        test_results.append(("模型管理器", False))
    
    # 并发工具测试
    try:
        print("\n📋 并发工具测试")
        result = run_concurrent_utils_tests()
        test_results.append(("并发工具", result))
    except Exception as e:
        print(f"❌ 并发工具测试失败: {e}")
        test_results.append(("并发工具", False))
    
    return test_results


def run_integration_tests():
    """运行集成测试"""
    print("\n🔗 集成测试")
    print("=" * 60)
    
    test_results = []
    
    # API端点测试
    try:
        print("\n📋 API端点测试")
        result = run_api_tests()
        test_results.append(("API端点", result))
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        test_results.append(("API端点", False))
    
    return test_results


def run_service_tests(service_url="http://localhost:8001"):
    """运行服务测试（需要运行中的服务）"""
    print(f"\n🚀 服务测试 ({service_url})")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 检查服务是否可用
        import requests
        response = requests.get(f"{service_url}/api/v1/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ 服务可用，开始集成测试")
            
            # 运行服务集成测试
            tester = YoloServiceTester(service_url)
            result = tester.run_all_tests()
            test_results.append(("服务集成", result))
        else:
            print(f"⚠️  服务响应异常: HTTP {response.status_code}")
            test_results.append(("服务集成", False))
            
    except requests.exceptions.RequestException:
        print("⚠️  服务不可用，跳过服务测试")
        print("   提示: 请先启动YOLO服务再运行服务测试")
        test_results.append(("服务集成", None))  # None表示跳过
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        test_results.append(("服务集成", False))
    
    return test_results


def print_test_summary(all_results):
    """打印测试摘要"""
    print("\n" + "=" * 60)
    print("📊 测试摘要")
    print("=" * 60)
    
    total_tests = 0
    passed_tests = 0
    skipped_tests = 0
    
    for category, results in all_results.items():
        print(f"\n📂 {category}")
        print("-" * 40)
        
        for test_name, result in results:
            total_tests += 1
            
            if result is True:
                print(f"✅ {test_name}")
                passed_tests += 1
            elif result is False:
                print(f"❌ {test_name}")
            elif result is None:
                print(f"⏭️  {test_name} (跳过)")
                skipped_tests += 1
            else:
                print(f"❓ {test_name} (未知状态)")
    
    # 计算统计信息
    failed_tests = total_tests - passed_tests - skipped_tests
    success_rate = (passed_tests / max(total_tests - skipped_tests, 1)) * 100
    
    print("\n" + "=" * 60)
    print("📈 统计信息")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {failed_tests}")
    print(f"跳过: {skipped_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 总体结果
    if failed_tests == 0:
        if skipped_tests == 0:
            print("\n🎉 所有测试通过！")
        else:
            print(f"\n✅ 所有可运行的测试通过！({skipped_tests} 个测试被跳过)")
        return True
    else:
        print(f"\n⚠️  有 {failed_tests} 个测试失败")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="YOLO服务测试运行器")
    parser.add_argument("--unit-only", action="store_true", help="只运行单元测试")
    parser.add_argument("--integration-only", action="store_true", help="只运行集成测试")
    parser.add_argument("--service-only", action="store_true", help="只运行服务测试")
    parser.add_argument("--service-url", default="http://localhost:8001", help="服务URL")
    parser.add_argument("--skip-service", action="store_true", help="跳过服务测试")
    
    args = parser.parse_args()
    
    if not HAS_ALL_TESTS:
        print("❌ 无法导入所有测试模块，请检查依赖项")
        return 1
    
    print("🧪 YOLO服务测试套件")
    print("=" * 60)
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_results = {}
    
    # 运行测试
    if not args.integration_only and not args.service_only:
        unit_results = run_unit_tests()
        all_results["单元测试"] = unit_results
    
    if not args.unit_only and not args.service_only:
        integration_results = run_integration_tests()
        all_results["集成测试"] = integration_results
    
    if not args.unit_only and not args.integration_only and not args.skip_service:
        service_results = run_service_tests(args.service_url)
        all_results["服务测试"] = service_results
    
    # 打印摘要
    success = print_test_summary(all_results)
    
    print(f"\n结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
