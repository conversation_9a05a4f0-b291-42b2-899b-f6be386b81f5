"""
并发工具测试

测试并发处理和请求队列功能
"""

import os
import sys
import unittest
import asyncio
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(Path(__file__).parent.parent))

from utils.concurrent_utils import RequestQueue, RequestInfo, RequestStatus, ConcurrentRequestProcessor


class TestRequestQueue(unittest.TestCase):
    """请求队列测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.queue = RequestQueue(max_size=5)
    
    def test_add_request(self):
        """测试添加请求"""
        request_info = RequestInfo(
            request_id="test_001",
            model_type="dipper",
            status=RequestStatus.PENDING,
            created_at=time.time()
        )
        
        # 添加请求
        success = self.queue.add_request(request_info)
        self.assertTrue(success)
        
        # 检查队列大小
        self.assertEqual(self.queue.get_queue_size(), 1)
        
        # 检查请求信息
        retrieved_info = self.queue.get_request_info("test_001")
        self.assertIsNotNone(retrieved_info)
        self.assertEqual(retrieved_info.request_id, "test_001")
        self.assertEqual(retrieved_info.model_type, "dipper")
    
    def test_queue_full(self):
        """测试队列满的情况"""
        # 填满队列
        for i in range(5):
            request_info = RequestInfo(
                request_id=f"test_{i:03d}",
                model_type="dipper",
                status=RequestStatus.PENDING,
                created_at=time.time()
            )
            success = self.queue.add_request(request_info)
            self.assertTrue(success)
        
        # 尝试添加第6个请求，应该失败
        extra_request = RequestInfo(
            request_id="test_extra",
            model_type="dipper",
            status=RequestStatus.PENDING,
            created_at=time.time()
        )
        success = self.queue.add_request(extra_request)
        self.assertFalse(success)
    
    def test_get_request(self):
        """测试获取请求"""
        # 添加请求
        request_info = RequestInfo(
            request_id="test_get",
            model_type="filter",
            status=RequestStatus.PENDING,
            created_at=time.time()
        )
        self.queue.add_request(request_info)
        
        # 获取请求
        retrieved_request = self.queue.get_request(timeout=1.0)
        self.assertIsNotNone(retrieved_request)
        self.assertEqual(retrieved_request.request_id, "test_get")
        self.assertEqual(retrieved_request.status, RequestStatus.PROCESSING)
    
    def test_get_request_timeout(self):
        """测试获取请求超时"""
        # 空队列，应该超时返回None
        retrieved_request = self.queue.get_request(timeout=0.1)
        self.assertIsNone(retrieved_request)
    
    def test_update_request(self):
        """测试更新请求状态"""
        # 添加请求
        request_info = RequestInfo(
            request_id="test_update",
            model_type="shaft",
            status=RequestStatus.PENDING,
            created_at=time.time()
        )
        self.queue.add_request(request_info)
        
        # 更新请求状态
        self.queue.update_request(
            request_id="test_update",
            status=RequestStatus.COMPLETED,
            result={"success": True, "devices": []}
        )
        
        # 检查更新结果
        updated_info = self.queue.get_request_info("test_update")
        self.assertEqual(updated_info.status, RequestStatus.COMPLETED)
        self.assertIsNotNone(updated_info.result)
        self.assertIsNotNone(updated_info.completed_at)
    
    def test_remove_request(self):
        """测试移除请求"""
        # 添加请求
        request_info = RequestInfo(
            request_id="test_remove",
            model_type="segmentation",
            status=RequestStatus.PENDING,
            created_at=time.time()
        )
        self.queue.add_request(request_info)
        
        # 确认请求存在
        self.assertIsNotNone(self.queue.get_request_info("test_remove"))
        
        # 移除请求
        self.queue.remove_request("test_remove")
        
        # 确认请求已移除
        self.assertIsNone(self.queue.get_request_info("test_remove"))


class TestConcurrentRequestProcessor(unittest.TestCase):
    """并发请求处理器测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.processor = ConcurrentRequestProcessor(max_workers=2, max_queue_size=10)
    
    def tearDown(self):
        """测试清理"""
        self.processor.stop()
    
    def test_processor_lifecycle(self):
        """测试处理器生命周期"""
        # 启动处理器
        self.processor.start()
        self.assertTrue(self.processor._running)
        
        # 停止处理器
        self.processor.stop()
        self.assertFalse(self.processor._running)
    
    def test_submit_request(self):
        """测试提交请求"""
        self.processor.start()
        
        # 定义一个简单的检测函数
        def mock_detector(image_path):
            time.sleep(0.1)  # 模拟处理时间
            return {"success": True, "devices": [], "processing_time": 0.1}
        
        # 提交请求
        request_id = self.processor.submit_request(
            model_type="dipper",
            detector_func=mock_detector,
            image_path="test_image.jpg"
        )
        
        self.assertIsNotNone(request_id)
        self.assertIsInstance(request_id, str)
        
        # 等待处理完成
        try:
            result = self.processor.wait_for_result(request_id, "dipper", timeout=5.0)
            self.assertIsNotNone(result)
            self.assertTrue(result["success"])
        except Exception as e:
            self.fail(f"请求处理失败: {e}")
    
    def test_request_timeout(self):
        """测试请求超时"""
        self.processor.start()
        
        # 定义一个慢速检测函数
        def slow_detector(image_path):
            time.sleep(2.0)  # 模拟长时间处理
            return {"success": True, "devices": []}
        
        # 提交请求
        request_id = self.processor.submit_request(
            model_type="filter",
            detector_func=slow_detector,
            image_path="test_image.jpg"
        )
        
        # 使用短超时等待结果，应该超时
        with self.assertRaises(TimeoutError):
            self.processor.wait_for_result(request_id, "filter", timeout=0.5)
    
    def test_statistics(self):
        """测试统计信息"""
        self.processor.start()
        
        # 获取初始统计信息
        stats = self.processor.get_statistics()
        self.assertIn("total_queues", stats)
        self.assertIn("running", stats)
        self.assertTrue(stats["running"])
        
        # 提交一些请求
        def quick_detector():
            return {"success": True}
        
        for i in range(3):
            self.processor.submit_request(
                model_type="dipper",
                detector_func=quick_detector
            )
        
        # 等待一下让请求被处理
        time.sleep(0.5)
        
        # 获取更新后的统计信息
        updated_stats = self.processor.get_statistics()
        self.assertIn("queues", updated_stats)
    
    def test_cleanup_old_requests(self):
        """测试清理旧请求"""
        self.processor.start()
        
        # 提交一个请求
        def simple_detector():
            return {"success": True}
        
        request_id = self.processor.submit_request(
            model_type="shaft",
            detector_func=simple_detector
        )
        
        # 等待处理完成
        time.sleep(0.5)
        
        # 清理旧请求（使用很短的时间阈值）
        cleaned_count = self.processor.cleanup_old_requests(max_age_seconds=0.1)
        
        # 应该清理了一些请求
        self.assertGreaterEqual(cleaned_count, 0)


def run_concurrent_utils_tests():
    """运行并发工具测试"""
    print("🧪 并发工具测试")
    print("-" * 40)
    
    # 运行请求队列测试
    print("📋 请求队列测试")
    queue_suite = unittest.TestLoader().loadTestsFromTestCase(TestRequestQueue)
    queue_runner = unittest.TextTestRunner(verbosity=1)
    queue_result = queue_runner.run(queue_suite)
    
    print("\n📋 并发处理器测试")
    processor_suite = unittest.TestLoader().loadTestsFromTestCase(TestConcurrentRequestProcessor)
    processor_runner = unittest.TextTestRunner(verbosity=1)
    processor_result = processor_runner.run(processor_suite)
    
    return queue_result.wasSuccessful() and processor_result.wasSuccessful()


if __name__ == "__main__":
    run_concurrent_utils_tests()
