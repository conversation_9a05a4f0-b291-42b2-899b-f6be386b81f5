"""
模型管理器测试

测试ModelManager的核心功能
"""

import os
import sys
import unittest
import tempfile
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(Path(__file__).parent.parent))

from core.model_manager import ModelManager


class TestModelManager(unittest.TestCase):
    """模型管理器测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.model_manager = ModelManager()
        
        # 创建临时模型文件
        self.temp_model_file = tempfile.NamedTemporaryFile(suffix='.pt', delete=False)
        self.temp_model_file.write(b"fake model data")
        self.temp_model_file.close()
        self.temp_model_path = self.temp_model_file.name
    
    def tearDown(self):
        """测试清理"""
        # 清理临时文件
        if os.path.exists(self.temp_model_path):
            os.unlink(self.temp_model_path)
        
        # 清理模型管理器
        self.model_manager.unload_all_models()
    
    def test_model_registration(self):
        """测试模型注册"""
        # 注册模型
        self.model_manager.register_model(
            model_name="test_model",
            model_path=self.temp_model_path,
            task_type="detect"
        )
        
        # 检查模型是否已注册
        registered_models = self.model_manager.get_registered_models()
        self.assertIn("test_model", registered_models)
        
        # 检查模型配置
        self.assertIn("test_model", self.model_manager._model_configs)
        config = self.model_manager._model_configs["test_model"]
        self.assertEqual(config["path"], self.temp_model_path)
        self.assertEqual(config["task_type"], "detect")
    
    def test_task_type_mapping(self):
        """测试任务类型映射"""
        # 测试segmentation -> segment映射
        self.model_manager.register_model(
            model_name="seg_model",
            model_path=self.temp_model_path,
            task_type="segmentation"
        )
        
        config = self.model_manager._model_configs["seg_model"]
        self.assertEqual(config["task_type"], "segment")
        
        # 测试detection -> detect映射
        self.model_manager.register_model(
            model_name="det_model",
            model_path=self.temp_model_path,
            task_type="detection"
        )
        
        config = self.model_manager._model_configs["det_model"]
        self.assertEqual(config["task_type"], "detect")
    
    def test_model_registration_invalid_path(self):
        """测试无效路径的模型注册"""
        with self.assertRaises(FileNotFoundError):
            self.model_manager.register_model(
                model_name="invalid_model",
                model_path="/nonexistent/path/model.pt",
                task_type="detect"
            )
    
    def test_statistics(self):
        """测试统计信息"""
        # 注册几个模型
        for i in range(3):
            self.model_manager.register_model(
                model_name=f"model_{i}",
                model_path=self.temp_model_path,
                task_type="detect"
            )
        
        # 获取统计信息
        stats = self.model_manager.get_all_stats()
        
        self.assertEqual(stats["total_registered"], 3)
        self.assertEqual(stats["total_loaded"], 0)  # 没有预加载
        self.assertIn("models", stats)
    
    def test_cache_management(self):
        """测试缓存管理"""
        # 设置较小的缓存大小
        self.model_manager.set_cache_config(max_cache_size=2, max_idle_time=1)
        
        # 注册模型
        self.model_manager.register_model(
            model_name="cache_test",
            model_path=self.temp_model_path,
            task_type="detect"
        )
        
        # 检查缓存配置
        self.assertEqual(self.model_manager._max_cache_size, 2)
        self.assertEqual(self.model_manager._max_idle_time, 1)
    
    def test_cleanup_functionality(self):
        """测试清理功能"""
        # 注册模型
        self.model_manager.register_model(
            model_name="cleanup_test",
            model_path=self.temp_model_path,
            task_type="detect"
        )
        
        # 测试清理空闲模型（由于没有实际加载，应该返回0）
        cleaned_count = self.model_manager.cleanup_idle_models()
        self.assertEqual(cleaned_count, 0)
    
    def test_batch_operations(self):
        """测试批量操作"""
        # 注册多个模型
        model_names = ["batch_1", "batch_2", "batch_3"]
        for name in model_names:
            self.model_manager.register_model(
                model_name=name,
                model_path=self.temp_model_path,
                task_type="detect"
            )
        
        # 测试批量预加载（会失败，因为是假的模型文件）
        results = self.model_manager.preload_models(model_names)
        
        # 所有模型都应该加载失败
        for name in model_names:
            self.assertIn(name, results)
            self.assertFalse(results[name])  # 预期失败
        
        # 测试批量卸载
        unload_results = self.model_manager.unload_all_models()
        self.assertIsInstance(unload_results, dict)


def run_model_manager_tests():
    """运行模型管理器测试"""
    print("🧪 模型管理器测试")
    print("-" * 40)
    
    suite = unittest.TestLoader().loadTestsFromTestCase(TestModelManager)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_model_manager_tests()
