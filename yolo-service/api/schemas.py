"""
API数据模型定义

定义FastAPI接口的请求和响应数据模型
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


class ModelType(str, Enum):
    """模型类型枚举"""
    DIPPER = "dipper"
    FILTER = "filter"
    SHAFT = "shaft"
    AEROBIC = "aerobic"
    PIEZHA = "piezha"
    SEGMENTATION = "segmentation"


class DetectionRequest(BaseModel):
    """检测请求模型"""
    model_type: ModelType = Field(..., description="模型类型")
    save_result: bool = Field(False, description="是否保存结果图像")
    conf: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度阈值")
    iou: Optional[float] = Field(None, ge=0.0, le=1.0, description="IoU阈值")
    imgsz: Optional[int] = Field(None, ge=320, le=1280, description="推理图像大小")


class BatchDetectionRequest(BaseModel):
    """批量检测请求模型"""
    model_type: ModelType = Field(..., description="模型类型")
    save_result: bool = Field(False, description="是否保存结果图像")
    conf: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度阈值")
    iou: Optional[float] = Field(None, ge=0.0, le=1.0, description="IoU阈值")
    imgsz: Optional[int] = Field(None, ge=320, le=1280, description="推理图像大小")


class Base64DetectionRequest(BaseModel):
    """Base64图像检测请求模型"""
    model_type: ModelType = Field(..., description="模型类型")
    image_data: str = Field(..., description="Base64编码的图像数据")
    save_result: bool = Field(False, description="是否保存结果图像")
    conf: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度阈值")
    iou: Optional[float] = Field(None, ge=0.0, le=1.0, description="IoU阈值")
    imgsz: Optional[int] = Field(None, ge=320, le=1280, description="推理图像大小")


class DeviceInfo(BaseModel):
    """设备信息模型"""
    id: Optional[int] = Field(None, description="设备ID")
    device_id: Optional[int] = Field(None, description="设备编号")
    status: str = Field(..., description="设备状态")
    confidence: float = Field(..., description="检测置信度")
    bbox: List[float] = Field(..., description="边界框坐标[x1,y1,x2,y2]")
    position: Optional[str] = Field(None, description="设备位置描述")


class OBBInfo(BaseModel):
    """OBB信息模型"""
    xyxyxyxy: Optional[List[float]] = Field(None, description="四个角点坐标")
    xywhr: Optional[List[float]] = Field(None, description="中心坐标+宽高+旋转角度")
    center: Optional[List[float]] = Field(None, description="中心坐标")
    size: Optional[List[float]] = Field(None, description="宽高")
    rotation: Optional[float] = Field(None, description="旋转角度")


class OBBDeviceInfo(DeviceInfo):
    """OBB设备信息模型"""
    obb: Optional[OBBInfo] = Field(None, description="OBB边界框信息")


class SegmentationObjectInfo(BaseModel):
    """分割对象信息模型"""
    id: int = Field(..., description="对象ID")
    class_name: str = Field(..., alias="class", description="对象类别")
    confidence: float = Field(..., description="检测置信度")
    bbox: List[float] = Field(..., description="边界框坐标[x1,y1,x2,y2]")
    mask_area: int = Field(..., description="分割区域面积")
    area_percentage: float = Field(..., description="面积百分比")


class DetectionResult(BaseModel):
    """检测结果模型"""
    success: bool = Field(..., description="检测是否成功")
    message: str = Field("", description="结果消息")
    model_name: str = Field(..., description="使用的模型名称")
    devices: List[DeviceInfo] = Field(default=[], description="检测到的设备列表")
    image_shape: Optional[List[int]] = Field(None, description="图像尺寸")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    saved_path: Optional[str] = Field(None, description="保存的文件路径")
    download_url: Optional[str] = Field(None, description="下载URL")


class OBBDetectionResult(DetectionResult):
    """OBB检测结果模型"""
    devices: List[OBBDeviceInfo] = Field(default=[], description="检测到的设备列表")


class SegmentationResult(BaseModel):
    """分割结果模型"""
    success: bool = Field(..., description="分割是否成功")
    message: str = Field("", description="结果消息")
    model_name: str = Field(..., description="使用的模型名称")
    objects: List[SegmentationObjectInfo] = Field(default=[], description="检测到的对象列表")
    objects_detail: List[SegmentationObjectInfo] = Field(default=[], description="对象详细信息")
    total_area: int = Field(0, description="总面积")
    segmented_area: int = Field(0, description="分割面积")
    percentage: float = Field(0.0, description="分割百分比")
    num_objects: int = Field(0, description="对象数量")
    class_stats: Optional[Dict[str, Any]] = Field(None, description="类别统计")
    image_shape: Optional[List[int]] = Field(None, description="图像尺寸")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    saved_path: Optional[str] = Field(None, description="保存的文件路径")
    download_url: Optional[str] = Field(None, description="下载URL")


class BatchDetectionResult(BaseModel):
    """批量检测结果模型"""
    success: bool = Field(..., description="批量检测是否成功")
    message: str = Field("", description="结果消息")
    total_images: int = Field(0, description="总图像数")
    successful_images: int = Field(0, description="成功处理的图像数")
    failed_images: int = Field(0, description="失败的图像数")
    results: List[Union[DetectionResult, SegmentationResult]] = Field(default=[], description="检测结果列表")
    processing_time: Optional[float] = Field(None, description="总处理时间(秒)")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    model_loaded: bool = Field(..., description="模型是否已加载")
    service_version: str = Field("1.0.0", description="服务版本")
    loaded_models: List[str] = Field(default=[], description="已加载的模型列表")
    enabled_services: Dict[str, bool] = Field(default={}, description="启用的服务")


class ModelInfo(BaseModel):
    """模型信息模型"""
    name: str = Field(..., description="模型名称")
    path: str = Field(..., description="模型路径")
    type: str = Field(..., description="模型类型")
    loaded: bool = Field(..., description="是否已加载")
    enabled: bool = Field(..., description="是否启用")


class ServiceStatus(BaseModel):
    """服务状态模型"""
    service_name: str = Field(..., description="服务名称")
    status: str = Field(..., description="服务状态")
    healthy: bool = Field(..., description="是否健康")
    message: str = Field("", description="状态消息")


class ConfigResponse(BaseModel):
    """配置响应模型"""
    models: Dict[str, str] = Field(..., description="模型路径配置")
    enabled_services: Dict[str, bool] = Field(..., description="启用的服务")
    max_workers: int = Field(..., description="最大工作线程数")
    max_file_size_mb: int = Field(..., description="最大文件大小(MB)")
    allowed_extensions: List[str] = Field(..., description="允许的文件扩展名")
    upload_dir: str = Field(..., description="上传目录")
    output_dir: str = Field(..., description="输出目录")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="操作是否成功")
    error: str = Field(..., description="错误信息")
    detail: Optional[str] = Field(None, description="错误详情")
    error_code: Optional[str] = Field(None, description="错误代码")


class CleanupRequest(BaseModel):
    """清理请求模型"""
    days: int = Field(7, ge=1, le=30, description="清理多少天前的文件")


class CleanupResponse(BaseModel):
    """清理响应模型"""
    success: bool = Field(..., description="清理是否成功")
    message: str = Field(..., description="清理消息")
    cleaned_files: int = Field(0, description="清理的文件数量")
    cutoff_days: int = Field(..., description="清理天数阈值")
