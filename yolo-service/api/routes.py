"""
API路由定义

定义所有的API端点和路由处理逻辑
"""

import os
import sys
import time
import uuid
import base64
import logging
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
import numpy as np
import cv2
from fastapi import APIRouter, File, UploadFile, HTTPException, Form, BackgroundTasks, Depends
from fastapi.responses import FileResponse, JSONResponse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging
from ..config.service_config import yolo_service_config
from ..core.model_manager import model_manager
from ..core.service_manager import service_manager
from .rate_limiter import concurrency_manager
from .schemas import *

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()


# 依赖函数
def get_detector_service(model_type: ModelType):
    """获取检测器服务"""
    service_name = f"{model_type.value}_detection"
    
    # 检查服务是否启用
    if not yolo_service_config.is_service_enabled(service_name):
        raise HTTPException(
            status_code=400,
            detail=f"服务未启用: {service_name}"
        )
    
    # 获取服务实例
    detector = service_manager.get_service(service_name)
    if detector is None:
        raise HTTPException(
            status_code=500,
            detail=f"服务不可用: {service_name}"
        )
    
    return detector


def validate_image_file(file: UploadFile) -> bool:
    """验证上传的图像文件"""
    if not file.filename:
        return False
    
    # 检查文件扩展名
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in yolo_service_config.allowed_extensions:
        return False
    
    # 检查文件大小
    if hasattr(file, 'size') and file.size > yolo_service_config.max_file_size:
        return False
    
    return True


def save_uploaded_file(file: UploadFile) -> str:
    """保存上传的文件"""
    try:
        # 生成唯一文件名
        file_ext = Path(file.filename).suffix.lower()
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = Path(yolo_service_config.upload_dir) / unique_filename
        
        # 确保上传目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            content = file.file.read()
            buffer.write(content)
        
        logger.info(f"文件已保存: {file_path}")
        return str(file_path)
    
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"保存文件失败: {str(e)}")


def generate_output_path(input_filename: str, model_type: str) -> str:
    """生成输出文件路径"""
    base_name = Path(input_filename).stem
    unique_id = str(uuid.uuid4())[:8]
    output_filename = f"{base_name}_{model_type}_{unique_id}_result.jpg"
    
    output_dir = Path(yolo_service_config.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    return str(output_dir / output_filename)


def cleanup_file(file_path: str):
    """清理临时文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"已清理文件: {file_path}")
    except Exception as e:
        logger.warning(f"清理文件失败 {file_path}: {e}")


# API端点
@router.get("/", response_model=dict)
async def root():
    """根端点"""
    return {
        "message": "YOLO推理服务API",
        "version": yolo_service_config.version,
        "title": yolo_service_config.title,
        "docs": "/docs",
        "health": "/health"
    }


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    try:
        loaded_models = model_manager.get_loaded_models()
        enabled_services = yolo_service_config.enabled_services
        
        # 检查关键服务状态
        all_healthy = True
        for service_name, enabled in enabled_services.items():
            if enabled:
                if not service_manager.is_service_running(service_name):
                    all_healthy = False
                    break
        
        return HealthResponse(
            status="healthy" if all_healthy else "degraded",
            model_loaded=len(loaded_models) > 0,
            service_version=yolo_service_config.version,
            loaded_models=loaded_models,
            enabled_services=enabled_services
        )
    
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return HealthResponse(
            status="unhealthy",
            model_loaded=False,
            service_version=yolo_service_config.version,
            loaded_models=[],
            enabled_services={}
        )


@router.post("/detect/upload", response_model=DetectionResult)
async def detect_uploaded_image(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="要检测的图像文件"),
    model_type: ModelType = Form(..., description="模型类型"),
    save_result: bool = Form(False, description="是否保存结果图像"),
    conf: Optional[float] = Form(None, ge=0.0, le=1.0, description="置信度阈值"),
    iou: Optional[float] = Form(None, ge=0.0, le=1.0, description="IoU阈值"),
    imgsz: Optional[int] = Form(None, ge=320, le=1280, description="推理图像大小")
):
    """
    上传图像文件进行检测
    """
    start_time = time.time()

    # 并发限制
    try:
        async with concurrency_manager.acquire_for_model(model_type.value, timeout=30.0):
            return await _process_upload_detection(
                background_tasks, file, model_type, save_result,
                conf, iou, imgsz, start_time
            )
    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=503,
            detail="服务繁忙，请稍后重试"
        )


async def _process_upload_detection(
    background_tasks: BackgroundTasks,
    file: UploadFile,
    model_type: ModelType,
    save_result: bool,
    conf: Optional[float],
    iou: Optional[float],
    imgsz: Optional[int],
    start_time: float
):
    """处理上传检测的内部函数"""
    try:
        # 验证文件
        if not validate_image_file(file):
            raise HTTPException(
                status_code=400,
                detail="无效的图像文件。支持的格式: " + ", ".join(yolo_service_config.allowed_extensions)
            )
        
        # 获取检测器服务
        detector = get_detector_service(model_type)
        
        # 保存上传的文件
        input_path = save_uploaded_file(file)
        
        # 生成输出路径
        output_path = generate_output_path(file.filename, model_type.value) if save_result else None
        
        # 准备推理参数
        inference_params = {}
        if conf is not None:
            inference_params['conf'] = conf
        if iou is not None:
            inference_params['iou'] = iou
        if imgsz is not None:
            inference_params['imgsz'] = imgsz

        # 执行检测
        if model_type == ModelType.SEGMENTATION:
            result = detector.detect(
                image=input_path,
                save_result=save_result,
                save_path=output_path,
                **inference_params
            )

            # 生成下载URL
            download_url = None
            if result.get("success") and save_result and "saved_path" in result:
                filename = Path(result["saved_path"]).name
                download_url = f"/download/{filename}"

            # 转换为分割结果格式
            response = SegmentationResult(
                success=result.get("success", False),
                message="图像分割成功" if result.get("success") else result.get("error", "分割失败"),
                model_name=result.get("model_name", model_type.value),
                objects=result.get("objects", []),
                objects_detail=result.get("objects_detail", []),
                total_area=result.get("total_area", 0),
                segmented_area=result.get("segmented_area", 0),
                percentage=result.get("percentage", 0.0),
                num_objects=result.get("num_objects", 0),
                class_stats=result.get("class_stats"),
                image_shape=result.get("image_shape"),
                processing_time=round(time.time() - start_time, 2),
                saved_path=result.get("saved_path"),
                download_url=download_url
            )
        else:
            result = detector.detect(
                image=input_path,
                save_result=save_result,
                save_path=output_path,
                **inference_params
            )
            
            # 生成下载URL
            download_url = None
            if result.get("success") and save_result and "saved_path" in result:
                filename = Path(result["saved_path"]).name
                download_url = f"/download/{filename}"

            response = DetectionResult(
                success=result.get("success", False),
                message="图像检测成功" if result.get("success") else result.get("error", "检测失败"),
                model_name=result.get("model_name", model_type.value),
                devices=result.get("devices", []),
                image_shape=result.get("image_shape"),
                processing_time=round(time.time() - start_time, 2),
                saved_path=result.get("saved_path"),
                download_url=download_url
            )
        
        # 添加清理任务
        background_tasks.add_task(cleanup_file, input_path)
        
        return response
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理图像时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理图像时出错: {str(e)}")


@router.post("/detect/base64", response_model=DetectionResult)
async def detect_base64_image(request: Base64DetectionRequest):
    """
    处理Base64编码的图像数据
    """
    start_time = time.time()

    # 并发限制
    try:
        async with concurrency_manager.acquire_for_model(request.model_type.value, timeout=30.0):
            return await _process_base64_detection(request, start_time)
    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=503,
            detail="服务繁忙，请稍后重试"
        )


async def _process_base64_detection(request: Base64DetectionRequest, start_time: float):
    """处理Base64检测的内部函数"""
    try:
        # 获取检测器服务
        detector = get_detector_service(request.model_type)
        
        # 检查Base64数据大小限制
        max_file_size = yolo_service_config.get_file_config().get('max_file_size', 10 * 1024 * 1024)  # 默认10MB
        estimated_size = len(request.image_data) * 3 // 4  # Base64编码大约增加33%的大小

        if estimated_size > max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"图像数据过大，估计大小: {estimated_size / 1024 / 1024:.1f}MB，最大允许: {max_file_size / 1024 / 1024:.1f}MB"
            )

        # 解码Base64数据
        try:
            # 移除可能的数据URL前缀
            image_data = request.image_data
            if image_data.startswith('data:image'):
                image_data = image_data.split(',')[1]

            image_bytes = base64.b64decode(image_data)

            # 转换为numpy数组
            image_array = np.frombuffer(image_bytes, dtype=np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

            if image is None:
                raise ValueError("无法解码图像数据")

        except Exception as e:
            raise HTTPException(status_code=400, detail=f"无效的Base64图像数据: {str(e)}")
        
        # 生成输出路径
        output_path = generate_output_path("base64_image", request.model_type.value) if request.save_result else None

        # 准备推理参数
        inference_params = {}
        if request.conf is not None:
            inference_params['conf'] = request.conf
        if request.iou is not None:
            inference_params['iou'] = request.iou
        if request.imgsz is not None:
            inference_params['imgsz'] = request.imgsz

        # 执行检测
        result = detector.detect(
            image=image,
            save_result=request.save_result,
            save_path=output_path,
            **inference_params
        )
        
        # 生成下载URL
        download_url = None
        if result.get("success") and request.save_result and "saved_path" in result:
            filename = Path(result["saved_path"]).name
            download_url = f"/download/{filename}"

        return DetectionResult(
            success=result.get("success", False),
            message="图像检测成功" if result.get("success") else result.get("error", "检测失败"),
            model_name=result.get("model_name", request.model_type.value),
            devices=result.get("devices", []),
            image_shape=result.get("image_shape"),
            processing_time=round(time.time() - start_time, 2),
            saved_path=result.get("saved_path"),
            download_url=download_url
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理Base64图像时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理Base64图像时出错: {str(e)}")


@router.get("/download/{filename}")
async def download_result(filename: str):
    """
    下载处理结果文件
    """
    file_path = Path(yolo_service_config.output_dir) / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")

    # 清理文件名，移除特殊字符
    safe_filename = filename.replace("%", "percent").replace(" ", "_")

    return FileResponse(
        path=str(file_path),
        filename=safe_filename,
        media_type="image/jpeg",
        headers={
            "Content-Disposition": f"attachment; filename=\"{safe_filename}\"",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET",
            "Access-Control-Allow-Headers": "*",
            "Cache-Control": "no-cache"
        }
    )


@router.get("/models", response_model=List[ModelInfo])
async def get_models():
    """
    获取模型信息列表
    """
    try:
        models = []
        registered_models = model_manager.get_registered_models()
        loaded_models = model_manager.get_loaded_models()

        for model_name in registered_models:
            model_config = model_manager._model_configs.get(model_name, {})
            models.append(ModelInfo(
                name=model_name,
                path=model_config.get("path", ""),
                type=model_config.get("task_type", "detect"),
                loaded=model_name in loaded_models,
                enabled=yolo_service_config.is_service_enabled(f"{model_name}_detection")
            ))

        return models

    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型信息失败: {str(e)}")


@router.get("/services", response_model=List[ServiceStatus])
async def get_services():
    """
    获取服务状态列表
    """
    try:
        services = []
        all_services = service_manager.get_all_services()

        for service_name, status in all_services.items():
            health_result = service_manager.health_check(service_name)
            services.append(ServiceStatus(
                service_name=service_name,
                status=status,
                healthy=health_result.get("healthy", False),
                message=health_result.get("message", "")
            ))

        return services

    except Exception as e:
        logger.error(f"获取服务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")


@router.get("/config", response_model=ConfigResponse)
async def get_config():
    """
    获取服务配置信息
    """
    try:
        return ConfigResponse(
            models=yolo_service_config.model_paths,
            enabled_services=yolo_service_config.enabled_services,
            max_workers=yolo_service_config.max_workers,
            max_file_size_mb=yolo_service_config.max_file_size // (1024 * 1024),
            allowed_extensions=yolo_service_config.allowed_extensions,
            upload_dir=yolo_service_config.upload_dir,
            output_dir=yolo_service_config.output_dir
        )

    except Exception as e:
        logger.error(f"获取配置信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置信息失败: {str(e)}")


@router.post("/cleanup", response_model=CleanupResponse)
async def cleanup_old_files(request: CleanupRequest):
    """
    清理旧的输出文件
    """
    try:
        import time
        from datetime import datetime, timedelta

        cutoff_time = time.time() - (request.days * 24 * 60 * 60)
        cleaned_count = 0

        # 清理输出目录
        output_dir = Path(yolo_service_config.output_dir)
        if output_dir.exists():
            for file_path in output_dir.rglob("*"):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                        except Exception as e:
                            logger.warning(f"删除文件失败 {file_path}: {e}")

        # 清理上传目录
        upload_dir = Path(yolo_service_config.upload_dir)
        if upload_dir.exists():
            for file_path in upload_dir.rglob("*"):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                        except Exception as e:
                            logger.warning(f"删除文件失败 {file_path}: {e}")

        return CleanupResponse(
            success=True,
            message=f"已清理 {cleaned_count} 个文件",
            cleaned_files=cleaned_count,
            cutoff_days=request.days
        )

    except Exception as e:
        logger.error(f"清理文件时出错: {e}")
        raise HTTPException(status_code=500, detail=f"清理文件时出错: {str(e)}")


# 异常处理器
@router.exception_handler(404)
async def not_found_handler(request, exc):
    """404错误处理器"""
    return JSONResponse(
        status_code=404,
        content={"detail": "请求的资源不存在", "path": str(request.url)}
    )


@router.exception_handler(500)
async def internal_server_error_handler(request, exc):
    """500错误处理器"""
    logger.error(f"内部服务器错误: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误，请稍后重试"}
    )
