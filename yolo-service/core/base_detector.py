"""
YOLO检测器基类

提供统一的检测器接口规范，基于现有YOLO服务的优秀实现设计
"""

import os
import sys
import time
import logging
import threading
from abc import ABC, abstractmethod
from typing import Dict, List, Union, Optional, Any
from pathlib import Path
import numpy as np
import cv2
from ultralytics import YOLO

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)

# 导入模型配置类（避免循环导入）
try:
    from ..config.model_config import ModelInfo, InferenceParams
except ImportError:
    # 如果导入失败，定义简单的类型
    from typing import NamedTuple

    class InferenceParams(NamedTuple):
        conf: float = 0.5
        iou: float = 0.4
        max_det: int = 10
        imgsz: int = 640
        device: str = "cpu"

        def to_dict(self):
            return self._asdict()

    class ModelInfo(NamedTuple):
        name: str
        path: str
        model_type: Any
        inference_params: InferenceParams


class BaseDetector(ABC):
    """
    YOLO检测器基类
    
    提供统一的接口规范和通用功能实现：
    - 线程安全的模型管理
    - 统一的图像输入处理
    - 标准化的结果格式
    - 错误处理和日志记录
    """
    
    def __init__(self, model_info: ModelInfo):
        """
        初始化检测器
        
        Args:
            model_info: 模型配置信息
        """
        self.model_info = model_info
        self.model_path = model_info.path
        self.inference_params = model_info.inference_params
        self._model = None
        self._lock = threading.RLock()  # 可重入锁
        
        # 验证模型文件
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        logger.info(f"{self.__class__.__name__} 初始化完成，模型: {self.model_path}")
    
    def _get_model(self) -> YOLO:
        """
        线程安全的模型获取
        
        Returns:
            YOLO: YOLO模型实例
        """
        with self._lock:
            if self._model is None:
                try:
                    task_type = "detect"
                    if self.model_info.model_type.value == "obb":
                        task_type = "obb"
                    elif self.model_info.model_type.value == "segmentation":
                        task_type = "segment"
                    
                    self._model = YOLO(self.model_path, task=task_type)
                    logger.info(f"模型加载成功: {self.model_path}, 任务类型: {task_type}")
                except Exception as e:
                    logger.error(f"模型加载失败: {self.model_path}, 错误: {e}")
                    raise
            
            return self._model
    
    def _validate_and_load_image(self, image: Union[np.ndarray, str]) -> tuple[np.ndarray, Optional[str]]:
        """
        验证并加载图像
        
        Args:
            image: 输入图像数组或图片路径
            
        Returns:
            tuple: (图像数组, 输入路径)
            
        Raises:
            FileNotFoundError: 图片路径不存在
            ValueError: 无法读取图片
            TypeError: 输入类型错误
        """
        input_path = None
        
        if isinstance(image, str):
            # 处理图片路径
            if not os.path.exists(image):
                raise FileNotFoundError(f"图片路径不存在: {image}")
            
            input_path = image
            image_array = cv2.imread(image)
            if image_array is None:
                raise ValueError(f"无法读取图片: {input_path}")
            
            return image_array, input_path
            
        elif isinstance(image, np.ndarray):
            # 处理numpy数组
            if image.size == 0:
                raise ValueError("输入图像为空")
            
            return image, input_path
            
        else:
            raise TypeError(f"image必须是np.ndarray或str类型，当前类型: {type(image)}")
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理（子类可重写）
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 处理后的图像
        """
        # 默认不进行预处理，直接返回原图
        return image
    
    def _run_inference(self, image: np.ndarray, custom_params: Optional[Dict[str, Any]] = None) -> Any:
        """
        执行推理

        Args:
            image: 输入图像
            custom_params: 自定义推理参数（不会修改共享对象）

        Returns:
            推理结果
        """
        model = self._get_model()

        # 创建参数副本，避免修改共享对象
        params = self.inference_params.to_dict().copy()

        # 应用自定义参数
        if custom_params:
            params.update(custom_params)

        logger.debug(f"推理参数: {params}")

        try:
            results = model(image, **params)
            return results
        except Exception as e:
            logger.error(f"推理失败: {e}")
            raise
    
    @abstractmethod
    def _parse_results(self, results: Any, image_shape: tuple) -> List[Dict[str, Any]]:
        """
        解析推理结果（子类必须实现）
        
        Args:
            results: 推理结果
            image_shape: 图像尺寸
            
        Returns:
            List[Dict]: 解析后的检测结果
        """
        pass
    
    def _save_result_image(self, results: Any, image: np.ndarray, 
                          save_path: str, devices: List[Dict]) -> str:
        """
        保存结果图像
        
        Args:
            results: 推理结果
            image: 原始图像
            save_path: 保存路径
            devices: 检测结果
            
        Returns:
            str: 保存的文件路径
        """
        try:
            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 绘制结果图像
            if hasattr(results[0], 'plot'):
                annotated_img = results[0].plot()
            else:
                annotated_img = image.copy()
            
            # 添加自定义标注（子类可重写此方法）
            annotated_img = self._add_custom_annotations(annotated_img, devices)
            
            # 保存图像
            cv2.imwrite(save_path, annotated_img)
            logger.info(f"结果图像已保存: {save_path}")
            
            return save_path
            
        except Exception as e:
            logger.error(f"保存结果图像失败: {e}")
            raise
    
    def _add_custom_annotations(self, image: np.ndarray, devices: List[Dict]) -> np.ndarray:
        """
        添加自定义标注（子类可重写）
        
        Args:
            image: 标注图像
            devices: 检测结果
            
        Returns:
            np.ndarray: 添加标注后的图像
        """
        # 默认不添加额外标注
        return image
    
    def _generate_save_path(self, input_path: Optional[str], 
                           save_dir: str = "tests") -> str:
        """
        生成保存路径
        
        Args:
            input_path: 输入图片路径
            save_dir: 保存目录
            
        Returns:
            str: 生成的保存路径
        """
        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        if input_path:
            base_name = os.path.basename(input_path)
            filename = f"{timestamp}_{base_name}"
        else:
            filename = f"{timestamp}_{self.model_info.name}_result.jpg"
        
        return os.path.join(save_dir, filename)
    
    def detect(self, image: Union[np.ndarray, str],
               save_result: bool = False,
               save_path: Optional[str] = None,
               **inference_kwargs) -> Dict[str, Any]:
        """
        执行检测（主要接口）

        Args:
            image: 输入图像数组或图片路径
            save_result: 是否保存结果图像
            save_path: 指定保存路径
            **inference_kwargs: 推理参数（conf, iou, imgsz等）

        Returns:
            Dict: 检测结果
        """
        start_time = time.time()

        try:
            # 验证并加载图像
            image_array, input_path = self._validate_and_load_image(image)

            # 图像预处理
            processed_image = self._preprocess_image(image_array)

            # 准备推理参数
            custom_params = {k: v for k, v in inference_kwargs.items()
                           if k in ['conf', 'iou', 'imgsz', 'max_det', 'device']}

            # 执行推理
            results = self._run_inference(processed_image, custom_params)
            
            # 解析结果
            devices = self._parse_results(results, processed_image.shape)
            
            # 构建响应
            response = {
                "success": True,
                "devices": devices,
                "image_shape": processed_image.shape,
                "processing_time": round(time.time() - start_time, 3),
                "model_name": self.model_info.name
            }
            
            # 保存结果图像
            if save_result:
                if save_path is None:
                    save_path = self._generate_save_path(input_path)

                saved_path = self._save_result_image(results, processed_image, save_path, devices)
                response["saved_path"] = saved_path
            
            return response
            
        except Exception as e:
            logger.error(f"检测失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "devices": [],
                "processing_time": round(time.time() - start_time, 3),
                "model_name": self.model_info.name
            }
    
    def __del__(self):
        """析构函数，清理资源"""
        if hasattr(self, '_model') and self._model is not None:
            del self._model
            logger.debug(f"{self.__class__.__name__} 模型资源已清理")
