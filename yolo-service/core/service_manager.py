"""
YOLO服务管理器

管理多个YOLO服务的生命周期，支持按需启动和配置化管理
"""

import os
import sys
import logging
import threading
from typing import Dict, List, Optional, Type, Any
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from server.utils.logger import setup_logging

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)


class ServiceManager:
    """
    YOLO服务管理器
    
    负责管理多个YOLO检测服务的生命周期：
    - 服务注册和发现
    - 按需启动和停止
    - 配置化管理
    - 健康检查
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化服务管理器"""
        if self._initialized:
            return
        
        self._services: Dict[str, Any] = {}  # 服务实例
        self._service_classes: Dict[str, Type] = {}  # 服务类
        self._service_configs: Dict[str, Dict] = {}  # 服务配置
        self._service_status: Dict[str, str] = {}  # 服务状态
        self._lock = threading.RLock()
        
        logger.info("服务管理器初始化完成")
        self._initialized = True
    
    def register_service(self, service_name: str, service_class: Type, 
                        config: Optional[Dict] = None) -> None:
        """
        注册服务
        
        Args:
            service_name: 服务名称
            service_class: 服务类
            config: 服务配置
        """
        with self._lock:
            self._service_classes[service_name] = service_class
            self._service_configs[service_name] = config or {}
            self._service_status[service_name] = "registered"
            
            logger.info(f"服务已注册: {service_name}")
    
    def start_service(self, service_name: str, **kwargs) -> bool:
        """
        启动服务
        
        Args:
            service_name: 服务名称
            **kwargs: 额外的启动参数
            
        Returns:
            bool: 启动是否成功
        """
        with self._lock:
            if service_name not in self._service_classes:
                logger.error(f"未注册的服务: {service_name}")
                return False
            
            if service_name in self._services:
                logger.warning(f"服务已启动: {service_name}")
                return True
            
            try:
                # 合并配置和启动参数
                config = self._service_configs[service_name].copy()
                config.update(kwargs)
                
                # 创建服务实例
                service_class = self._service_classes[service_name]
                service_instance = service_class(**config)
                
                self._services[service_name] = service_instance
                self._service_status[service_name] = "running"
                
                logger.info(f"服务启动成功: {service_name}")
                return True
                
            except Exception as e:
                logger.error(f"服务启动失败: {service_name}, 错误: {e}")
                self._service_status[service_name] = "failed"
                return False
    
    def stop_service(self, service_name: str) -> bool:
        """
        停止服务

        Args:
            service_name: 服务名称

        Returns:
            bool: 停止是否成功
        """
        with self._lock:
            if service_name not in self._services:
                logger.warning(f"服务未运行: {service_name}")
                return True

            try:
                service_instance = self._services[service_name]

                # 安全地停止服务
                success = self._safe_stop_service(service_instance, service_name)

                # 从服务字典中移除
                self._services.pop(service_name, None)
                self._service_status[service_name] = "stopped"

                logger.info(f"服务停止成功: {service_name}")
                return success

            except Exception as e:
                logger.error(f"服务停止失败: {service_name}, 错误: {e}")
                # 即使出错也要清理服务引用
                self._services.pop(service_name, None)
                self._service_status[service_name] = "error"
                return False

    def _safe_stop_service(self, service_instance: Any, service_name: str) -> bool:
        """
        安全地停止服务实例

        Args:
            service_instance: 服务实例
            service_name: 服务名称

        Returns:
            bool: 停止是否成功
        """
        try:
            # 1. 尝试调用服务的停止方法
            if hasattr(service_instance, 'stop') and callable(getattr(service_instance, 'stop')):
                try:
                    service_instance.stop()
                    logger.debug(f"调用了服务 {service_name} 的stop方法")
                except Exception as e:
                    logger.warning(f"服务 {service_name} 的stop方法执行失败: {e}")

            # 2. 尝试调用服务的清理方法
            if hasattr(service_instance, 'cleanup') and callable(getattr(service_instance, 'cleanup')):
                try:
                    service_instance.cleanup()
                    logger.debug(f"调用了服务 {service_name} 的cleanup方法")
                except Exception as e:
                    logger.warning(f"服务 {service_name} 的cleanup方法执行失败: {e}")

            # 3. 尝试关闭服务的资源
            if hasattr(service_instance, 'close') and callable(getattr(service_instance, 'close')):
                try:
                    service_instance.close()
                    logger.debug(f"调用了服务 {service_name} 的close方法")
                except Exception as e:
                    logger.warning(f"服务 {service_name} 的close方法执行失败: {e}")

            # 4. 清理服务的特定属性（如果是检测器）
            if hasattr(service_instance, '_model'):
                try:
                    # 不直接删除，而是设置为None让垃圾回收器处理
                    service_instance._model = None
                    logger.debug(f"清理了服务 {service_name} 的模型引用")
                except Exception as e:
                    logger.warning(f"清理服务 {service_name} 的模型引用失败: {e}")

            return True

        except Exception as e:
            logger.error(f"安全停止服务 {service_name} 时发生异常: {e}")
            return False

    def restart_service(self, service_name: str, **kwargs) -> bool:
        """
        重启服务
        
        Args:
            service_name: 服务名称
            **kwargs: 额外的启动参数
            
        Returns:
            bool: 重启是否成功
        """
        logger.info(f"重启服务: {service_name}")
        
        # 先停止服务
        if not self.stop_service(service_name):
            return False
        
        # 再启动服务
        return self.start_service(service_name, **kwargs)
    
    def get_service(self, service_name: str) -> Optional[Any]:
        """
        获取服务实例
        
        Args:
            service_name: 服务名称
            
        Returns:
            服务实例或None
        """
        with self._lock:
            return self._services.get(service_name)
    
    def is_service_running(self, service_name: str) -> bool:
        """
        检查服务是否运行
        
        Args:
            service_name: 服务名称
            
        Returns:
            bool: 服务是否运行
        """
        with self._lock:
            return (service_name in self._services and 
                   self._service_status.get(service_name) == "running")
    
    def get_service_status(self, service_name: str) -> str:
        """
        获取服务状态
        
        Args:
            service_name: 服务名称
            
        Returns:
            str: 服务状态
        """
        with self._lock:
            return self._service_status.get(service_name, "unknown")
    
    def get_all_services(self) -> Dict[str, str]:
        """
        获取所有服务状态
        
        Returns:
            Dict[str, str]: 服务名称到状态的映射
        """
        with self._lock:
            return self._service_status.copy()
    
    def get_running_services(self) -> List[str]:
        """
        获取运行中的服务列表
        
        Returns:
            List[str]: 运行中的服务名称列表
        """
        with self._lock:
            return [name for name, status in self._service_status.items() 
                   if status == "running"]
    
    def health_check(self, service_name: str) -> Dict[str, Any]:
        """
        服务健康检查
        
        Args:
            service_name: 服务名称
            
        Returns:
            Dict: 健康检查结果
        """
        with self._lock:
            if service_name not in self._services:
                return {
                    "service": service_name,
                    "status": "not_running",
                    "healthy": False,
                    "message": "服务未运行"
                }
            
            try:
                service_instance = self._services[service_name]
                
                # 如果服务有健康检查方法，调用它
                if hasattr(service_instance, 'health_check'):
                    return service_instance.health_check()
                
                # 默认健康检查
                return {
                    "service": service_name,
                    "status": self._service_status.get(service_name, "unknown"),
                    "healthy": True,
                    "message": "服务运行正常"
                }
                
            except Exception as e:
                logger.error(f"健康检查失败: {service_name}, 错误: {e}")
                return {
                    "service": service_name,
                    "status": "error",
                    "healthy": False,
                    "message": f"健康检查失败: {str(e)}"
                }
    
    def start_enabled_services(self, config: Dict[str, Any]) -> Dict[str, bool]:
        """
        根据配置启动启用的服务
        
        Args:
            config: 服务配置
            
        Returns:
            Dict[str, bool]: 服务启动结果
        """
        enabled_services = config.get('enabled_services', {})
        results = {}
        
        for service_name, enabled in enabled_services.items():
            if enabled:
                logger.info(f"启动服务: {service_name}")
                results[service_name] = self.start_service(service_name)
            else:
                logger.info(f"跳过服务: {service_name} (未启用)")
                results[service_name] = False
        
        return results
    
    def stop_all_services(self) -> Dict[str, bool]:
        """
        停止所有服务

        Returns:
            Dict[str, bool]: 服务停止结果
        """
        results = {}

        # 获取所有运行中的服务（创建副本避免在迭代时修改字典）
        with self._lock:
            running_services = list(self._services.keys())

        # 逐个停止服务
        for service_name in running_services:
            try:
                results[service_name] = self.stop_service(service_name)
            except Exception as e:
                logger.error(f"停止服务 {service_name} 时发生异常: {e}")
                results[service_name] = False

        # 最后清理，确保所有服务都被移除
        with self._lock:
            remaining_services = list(self._services.keys())
            for service_name in remaining_services:
                logger.warning(f"强制清理残留服务: {service_name}")
                self._services.pop(service_name, None)
                self._service_status[service_name] = "force_stopped"

        return results
    
    def shutdown(self):
        """
        优雅关闭服务管理器

        这是推荐的清理方法，比析构函数更可靠
        """
        try:
            logger.info("开始关闭服务管理器...")

            # 停止所有服务
            results = self.stop_all_services()

            # 统计结果
            total_services = len(results)
            successful_stops = sum(1 for success in results.values() if success)

            logger.info(f"服务管理器关闭完成: {successful_stops}/{total_services} 个服务成功停止")

            # 清理内部状态
            with self._lock:
                self._services.clear()
                self._service_classes.clear()
                self._service_configs.clear()
                # 保留状态信息用于调试

        except Exception as e:
            logger.error(f"服务管理器关闭失败: {e}")

    def __del__(self):
        """析构函数，尝试清理资源"""
        try:
            # 只做最基本的清理，避免在析构时出现复杂错误
            if hasattr(self, '_services') and self._services:
                logger.warning("服务管理器在析构时仍有活跃服务，建议使用shutdown()方法")
                # 简单清理，不调用复杂的停止逻辑
                self._services.clear()
        except Exception:
            # 析构函数中不应该抛出异常
            pass


# 创建全局服务管理器实例
service_manager = ServiceManager()
